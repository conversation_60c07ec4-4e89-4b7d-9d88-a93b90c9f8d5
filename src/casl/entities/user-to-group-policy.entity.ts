import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { PartialIndexWithSoftDelete } from '../../common/decorators/typeorm.decorator';
import { BaseEntityWithoutUpdateAndVersion } from '../../common/entities/base.entity';
import { GroupPolicy } from './group-policy.entity';

@Entity({ name: 'user_to_group_policy' })
@PartialIndexWithSoftDelete(['userId', 'groupPolicyId'], {
  unique: true,
})
export class UserToGroupPolicy extends BaseEntityWithoutUpdateAndVersion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @ManyToOne(() => User, (user) => user.userToGroupPolicies, {
    onDelete: 'CASCADE',
    cascade: ['insert'],
  })
  @JoinC<PERSON>umn({ name: 'user_id' })
  user: User;

  @Column({ name: 'group_policy_id' })
  groupPolicyId: number;

  @ManyToOne(
    () => GroupPolicy,
    (groupPolicy) => groupPolicy.userToGroupPolicies,
    { onDelete: 'CASCADE', cascade: ['insert'] },
  )
  @JoinColumn({ name: 'group_policy_id' })
  groupPolicy: GroupPolicy;
}

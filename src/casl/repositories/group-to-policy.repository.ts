import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { I18nPath } from '../../i18n/i18n.generated';
import { GroupToPolicy } from '../entities/group-to-policy.entity';

@Injectable()
export class GroupToPolicyRepository extends BaseRepository<GroupToPolicy> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(GroupToPolicy, dataSource);
    this.entityNameI18nKey = 'common.word.groupToPolicy';
  }
}

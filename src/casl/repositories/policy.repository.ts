import { Injectable } from '@nestjs/common';
import { NotFoundExc } from 'src/common/exceptions/custom.exception';
import { DataSource } from 'typeorm';
import { Action, ActionAbility, Resource } from '../../common/enums/casl.enum';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { I18nPath } from '../../i18n/i18n.generated';
import { Policy } from '../entities/policy.entity';

@Injectable()
export class PolicyRepository extends BaseRepository<Policy> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(Policy, dataSource);
    this.entityNameI18nKey = 'common.word.policy';
  }

  async getPolicyByIdsAndCheckErr(policyIds: number[]) {
    const policies = await Promise.all(
      policyIds.map(async (policyId) => this.findOneBy({ id: policyId })),
    );

    //  Check policies exist
    policies.forEach((policiesEntity) => {
      if (!policiesEntity) throw new NotFoundExc({ message: 'common' });
    });
    return policies;
  }

  genName(action: Action, resource: Resource, actionAbility: ActionAbility) {
    return `${actionAbility} ${action} ${resource}`;
  }
}

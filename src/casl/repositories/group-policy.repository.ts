import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { I18nPath } from '../../i18n/i18n.generated';
import { GroupPolicy } from '../entities/group-policy.entity';

@Injectable()
export class GroupPolicyRepository extends BaseRepository<GroupPolicy> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(GroupPolicy, dataSource);
    this.entityNameI18nKey = 'common.word.groupPolicy';
  }
}

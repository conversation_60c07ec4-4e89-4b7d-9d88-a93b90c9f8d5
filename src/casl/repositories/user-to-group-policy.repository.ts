import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { I18nPath } from '../../i18n/i18n.generated';
import { UserToGroupPolicy } from '../entities/user-to-group-policy.entity';

@Injectable()
export class UserToGroupPolicyRepository extends BaseRepository<UserToGroupPolicy> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(UserToGroupPolicy, dataSource);
    this.entityNameI18nKey = 'common.word.userToGroupPolicy';
  }
}

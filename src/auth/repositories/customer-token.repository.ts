import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { I18nPath } from '../../i18n/i18n.generated';
import { CustomerToken } from '../entities/customer-token.entity';

@Injectable()
export class CustomerTokenRepository extends BaseRepository<CustomerToken> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(CustomerToken, dataSource);
    this.entityNameI18nKey = 'common.word.customerToken';
  }
}

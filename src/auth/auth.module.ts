import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { forwardRef } from '@nestjs/common/utils';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmCustomModule } from 'utility/dist';
import { GroupPolicyRepository } from '../casl/repositories/group-policy.repository';
import { UserToGroupPolicyRepository } from '../casl/repositories/user-to-group-policy.repository';
import { bullQueues } from '../common/config/bull.config';
import { GlobalConfig } from '../common/config/global.config';
import { FileModule } from '../file/file.module';
import { FileRepository } from '../file/repositories/file.repository';
import { UtilsModule } from '../utils/utils.module';
import { AuthAdminController } from './controllers/admin/auth.admin.controller';
import { ProfileAdminController } from './controllers/admin/profile.admin.controller';
import { AuthCustomerController } from './controllers/customer/auth.customer.controller';
import { ProfileCustomerController } from './controllers/customer/profile.customer.controller';
import { AdminRepository } from './repositories/admin.repository';
import { CustomerTokenRepository } from './repositories/customer-token.repository';
import { CustomerRepository } from './repositories/customer.repository';
import { UserGroupToUserRepository } from './repositories/user-group-to-user.repository';
import { UserGroupRepository } from './repositories/user-group.repository';
import { UserRepository } from './repositories/user.repository';
import { AdminAdminService } from './services/admin/admin.admin.service';
import { AuthAdminService } from './services/admin/auth.admin.service';
import { CustomerAdminService } from './services/admin/customer.admin.service';
import { ProfileAdminService } from './services/admin/profile.admin.service';
import { AuthCommonService } from './services/common/auth.common.service';
import { AuthCustomerService } from './services/customer/auth.customer.service';
import { ProfileCustomerService } from './services/customer/profile.customer.service';
import { UserGroupCustomerService } from './services/customer/user-group.customer.service';
import { JwtAuthenAdminStrategy } from './strategies/jwt-authen.admin.strategy';
import { JwtAuthenCustomerStrategy } from './strategies/jwt-authen.customer.strategy';
import { JwtAuthenUserStrategy } from './strategies/jwt-authen.user.strategy';
import { AdminAdminController } from './controllers/admin/admin.admin.controller';
import { CustomerAdminController } from './controllers/admin/customer.admin.controller';

@Module({
  imports: [
    HttpModule,
    PassportModule,
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService<GlobalConfig>) => ({
        secret: configService.get('auth.accessToken.secret'),
        signOptions: {
          algorithm: configService.get('auth.accessToken.algorithm'),
        },
      }),
    }),
    BullModule.registerQueue(...bullQueues),
    TypeOrmCustomModule.forFeature([
      UserRepository,
      CustomerRepository,
      AdminRepository,
      CustomerTokenRepository,
      UserGroupToUserRepository,
      UserGroupRepository,
      GroupPolicyRepository,
      UserToGroupPolicyRepository,
      FileRepository,
    ]),
    UtilsModule,
    forwardRef(() => FileModule),
  ],
  controllers: [
    AuthAdminController,
    ProfileAdminController,
    AdminAdminController,
    CustomerAdminController,
    AuthCustomerController,
    ProfileCustomerController,
  ],
  providers: [
    JwtAuthenAdminStrategy,
    JwtAuthenCustomerStrategy,
    JwtAuthenUserStrategy,
    AuthAdminService,
    AuthCustomerService,
    AdminAdminService,
    CustomerAdminService,
    ProfileAdminService,
    ProfileCustomerService,
    AuthCommonService,
    UserGroupCustomerService,
  ],
  exports: [],
})
export class AuthModule {}

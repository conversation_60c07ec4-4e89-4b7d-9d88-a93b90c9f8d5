import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Transactional } from 'typeorm-transactional';
import { GlobalConfig } from '../../../common/config/global.config';
import { ExceptionSubCode } from '../../../common/constants/exception.constant';
import {
  ConflictExc,
  ExpectationFailedExc,
  NotFoundExc,
  UnauthorizedExc,
} from '../../../common/exceptions/custom.exception';
import { EncryptService } from '../../../utils/services/encrypt.service';
import { RefreshTokenReqDto } from '../../dtos/common/req/auth.req.dto';
import { AuthTokenResDto } from '../../dtos/common/res/auth-token.res.dto';
import {
  CheckPhoneNumberCustomerReqDto,
  LoginCustomerReqDto,
  RegisterCustomerReqDto,
  ResetPasswordCustomerReqDto,
} from '../../dtos/customer/req/auth.customer.req.dto';
import { CheckPhoneNumberCustomerResDto } from '../../dtos/customer/res/auth.customer.res.dto';
import { CustomerToken } from '../../entities/customer-token.entity';
import { CustomerTokenType } from '../../enums/customer-token.enum';
import { CustomerStatus } from '../../enums/customer.enum';
import { UserType } from '../../enums/user.enum';
import { JwtAuthPayload } from '../../interfaces/jwt-payload.interface';
import { CustomerTokenRepository } from '../../repositories/customer-token.repository';
import { CustomerRepository } from '../../repositories/customer.repository';
import { UserRepository } from '../../repositories/user.repository';
import { AuthCommonService } from '../common/auth.common.service';
import { AppResponseDto } from 'src/common/dtos/app-response.dto';

@Injectable()
export class AuthCustomerService {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService<GlobalConfig>,
    private encryptService: EncryptService,
    private authCommonService: AuthCommonService,
    private customerRepo: CustomerRepository,
    private userRepo: UserRepository,
    private customerTokenRepo: CustomerTokenRepository,
  ) {}

  async checkPhoneNumber(dto: CheckPhoneNumberCustomerReqDto) {
    const { phoneNumber } = dto;

    const result = await this.customerRepo.findOneBy({
      phoneNumber,
    });

    return new CheckPhoneNumberCustomerResDto({ isExisted: Boolean(result) });
  }

  async login(dto: LoginCustomerReqDto) {
    const { password, phoneNumber } = dto;

    const customer = await this.customerRepo.findOne({
      where: { phoneNumber },
    });

    if (!customer)
      throw new UnauthorizedExc({ message: 'auth.customer.customerNotFound' });

    if (!password) {
      throw new UnauthorizedExc({
        message: 'auth.customer.passwordRequired',
      });
    }

    if (!this.encryptService.compareHash(password, customer.password)) {
      throw new UnauthorizedExc({ message: 'auth.customer.failPassword' });
    }

    customer.lastVisitDate = new Date();
    await this.customerRepo.save(customer);

    const payload: JwtAuthPayload = { userId: customer.userId };
    const accessToken = this.authCommonService.generateAccessToken(payload);
    const refreshToken = this.authCommonService.generateRefreshToken(payload);

    return new AppResponseDto(
      AuthTokenResDto.forCustomer({ data: { accessToken, refreshToken } }),
    );
  }

  @Transactional()
  async register(dto: RegisterCustomerReqDto) {
    const { phoneNumber, password, name } = dto;

    let customer = await this.customerRepo.findFirst({
      where: { phoneNumber },
    });
    if (customer) throw new ConflictExc({ message: 'auth.customer.existed' });

    const user = this.userRepo.create({ type: UserType.CUSTOMER });
    await this.userRepo.insert(user);

    if (!password)
      throw new ConflictExc({
        message: 'auth.customer.passwordRequired',
      });

    customer = this.customerRepo.create({
      phoneNumber,
      userId: user.id,
      name,
      password: this.encryptService.encryptText(password),
    });

    await this.customerRepo.insert(customer);

    const payload: JwtAuthPayload = { userId: customer.userId };
    const accessToken = this.authCommonService.generateAccessToken(payload);
    const refreshToken = this.authCommonService.generateRefreshToken(payload);

    return new AppResponseDto(
      AuthTokenResDto.forCustomer({ data: { accessToken, refreshToken } }),
    );
  }

  async refreshToken(dto: RefreshTokenReqDto) {
    const { refreshToken } = dto;

    try {
      const payload = this.jwtService.verify<JwtAuthPayload>(refreshToken, {
        secret: this.configService.get('auth.refreshToken.secret'),
      });
      const accessToken = this.authCommonService.generateAccessToken({
        userId: payload.userId,
      });

      return new AppResponseDto(
        AuthTokenResDto.forCustomer({ data: { accessToken } }),
      );
    } catch (error) {
      throw new UnauthorizedExc({
        subCode: ExceptionSubCode.INVALID_REFRESH_TOKEN,
        message: 'common',
      });
    }
  }

  @Transactional()
  async resetPassword(dto: ResetPasswordCustomerReqDto) {
    const { phoneNumber, otp, newPassword } = dto;

    const { customer } = await this.getAndCheckCustomer({
      phoneNumber,
    });
    const customerToken = await this.customerTokenRepo.findFirst({
      where: {
        type: CustomerTokenType.RESET_PASSWORD,
        phoneNumber,
      },
      order: { createdAt: 'desc' },
    });

    this.checkCustomerToken({ otp, customerToken });

    await this.customerRepo.update(customer.id, {
      password: this.encryptService.encryptText(newPassword),
    });

    const payload: JwtAuthPayload = { userId: customer.userId };
    const accessToken = this.authCommonService.generateAccessToken(payload);
    const refreshToken = this.authCommonService.generateRefreshToken(payload);

    return new AppResponseDto(
      AuthTokenResDto.forCustomer({ data: { accessToken, refreshToken } }),
    );
  }

  private async getAndCheckCustomer({
    phoneNumber,
  }: GetAndCheckCustomerParams) {
    const customer = await this.customerRepo.findFirst({
      where: {
        phoneNumber,
        status: CustomerStatus.ACTIVE,
      },
      order: { createdAt: 'DESC' },
    });
    if (!customer)
      throw new NotFoundExc({ message: 'auth.customer.customerNotFound' });

    return { customer };
  }

  private checkCustomerToken({ otp, customerToken }: CheckCustomerTokenParams) {
    if (!customerToken)
      throw new NotFoundExc({
        message: ['common.word.notFound', 'common.word.customerToken'],
      });

    if (customerToken.expiresAt < new Date())
      throw new ExpectationFailedExc({ message: 'token.otpExpires' });

    if (customerToken.token !== otp)
      throw new ExpectationFailedExc({ message: 'token.incorrectOtp' });
  }
}

interface GetAndCheckCustomerParams {
  phoneNumber: string;
}

interface CheckCustomerTokenParams {
  customerToken: CustomerToken | null;
  otp: string;
}

import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Transactional } from 'typeorm-transactional';
import { EventName } from '../../../common/enums/event.enum';
import { User } from '../../entities/user.entity';
import { UserGroupToUserRepository } from '../../repositories/user-group-to-user.repository';

@Injectable()
export class UserGroupCustomerService {
  constructor(private userGroupToUserRepo: UserGroupToUserRepository) {}

  @OnEvent(EventName.CUSTOMER_DELETED)
  @Transactional()
  async deleteFromGroupWhenCustomerDeleted(user: User) {
    await this.userGroupToUserRepo.softDelete({
      userId: user.id,
    });
  }
}

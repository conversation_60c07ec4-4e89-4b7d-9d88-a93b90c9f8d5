import { Transform } from 'class-transformer';
import {
  IsValidDate,
  IsValidEmail,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { getPhoneE164 } from '../../../../common/utils';

export class UpdateProfileCustomerReqDto {
  @IsValidText({
    message: 'auth.customer.wrongPhoneNumber',
    minLength: 12,
    maxLength: 12,
  })
  @Transform(({ value }) => getPhoneE164(value))
  phoneNumber: string;

  @IsValidText({ maxLength: 50, required: false })
  name?: string;

  @IsValidEmail({ required: false })
  email?: string;

  @IsValidText({ maxLength: 255, required: false, trim: true })
  address?: string;

  @IsValidDate({ required: false })
  birthDate?: Date;

  @IsValidText({ maxLength: 255, required: false })
  avatarUrl: string;
}

export class UpdatePasswordCustomerReqDto {
  @IsValidText({ minLength: 6, maxLength: 50 })
  password: string;

  @IsValidText({ minLength: 6, maxLength: 50 })
  newPassword: string;
}

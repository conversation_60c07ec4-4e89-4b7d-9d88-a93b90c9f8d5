import { BaseResponseDtoParams } from '../../../../common/dtos/base.res';
import { UserGroupToUser } from '../../../entities/user-group-to-user.entity';

interface UserGroupToUserResDtoParams extends BaseResponseDtoParams {
  data: UserGroupToUser;
}

export class UserGroupToUserResDto {
  id: number;
  userGroupId: number;
  userId: number;

  static mapProperty(
    dto: UserGroupToUserResDto,
    { data }: UserGroupToUserResDtoParams,
  ) {
    dto.id = data.id;
    dto.userGroupId = data.userGroupId;
    dto.userId = data.userId;
  }
}

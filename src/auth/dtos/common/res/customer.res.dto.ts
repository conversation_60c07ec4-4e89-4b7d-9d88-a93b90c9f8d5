import {
  BaseResponseDtoParams,
  ResOptionDto,
} from '../../../../common/dtos/base.res';
import { Customer } from '../../../entities/customer.entity';
import { CustomerGender, CustomerStatus } from '../../../enums/customer.enum';
import { UserResDto } from './user.res.dto';

export interface CustomerResDtoParams extends BaseResponseDtoParams {
  dto?: CustomerResDto;
  data?: Customer;
  resOpts?: ResOptionDto;
}

export class CustomerResDto {
  id: number;
  phoneNumber: string;
  email: string;
  name: string;
  address: string;
  birthDate: Date;
  status: CustomerStatus;
  user: UserResDto;
  userId: number;
  lastVisitDate: Date;
  gender: CustomerGender;
  createdAt: Date;
  avatarUrl: string;

  static mapProperty({ dto, data }: CustomerResDtoParams) {
    dto.id = data.id;
    dto.phoneNumber = data.phoneNumber;
    dto.email = data.email;
    dto.name = data.name;
    dto.address = data.address;
    dto.birthDate = data.birthDate;
    dto.lastVisitDate = data.lastVisitDate;
    dto.createdAt = data.createdAt;
    dto.gender = data.gender;
    dto.userId = data.userId;
    dto.avatarUrl = data.avatarUrl;
  }

  static forCustomer({ data, resOpts }: CustomerResDtoParams) {
    if (!data) return null;

    const result = new CustomerResDto();

    this.mapProperty({
      dto: result,
      data: data,
    });

    return result;
  }

  static forAdmin(params: CustomerResDtoParams) {
    const { data, resOpts } = params;

    const result = new CustomerResDto();
    if (!data) return null;

    this.mapProperty({ dto: result, data: data });

    result.status = data.status;

    result.user = UserResDto.forAdmin({ data: data.user, resOpts });

    return result;
  }
}

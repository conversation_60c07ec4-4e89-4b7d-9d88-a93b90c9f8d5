import { Body, Controller, Get, Post } from '@nestjs/common';
import { Query } from '@nestjs/common/decorators';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import { RefreshTokenReqDto } from '../../dtos/common/req/auth.req.dto';
import {
  CheckPhoneNumberCustomerReqDto,
  LoginCustomerReqDto,
  RegisterCustomerReqDto,
  ResetPasswordCustomerReqDto,
} from '../../dtos/customer/req/auth.customer.req.dto';
import { AuthCustomerService } from '../../services/customer/auth.customer.service';

@Controller(`${PrefixType.CUSTOMER}/auth`)
@ApiTags('Auth Customer')
export class AuthCustomerController {
  constructor(private authCustomerService: AuthCustomerService) {}

  @Post('register')
  register(@Body() body: RegisterCustomerReqDto) {
    return this.authCustomerService.register(body);
  }

  @Post('login')
  login(@Body() body: LoginCustomerReqDto) {
    return this.authCustomerService.login(body);
  }

  @Post('refresh-token')
  refreshToken(@Body() body: RefreshTokenReqDto) {
    return this.authCustomerService.refreshToken(body);
  }

  @Get('is-phone-existed')
  checkPhoneNumber(@Query() query: CheckPhoneNumberCustomerReqDto) {
    return this.authCustomerService.checkPhoneNumber(query);
  }

  @Post('reset-password')
  resetPassword(@Body() body: ResetPasswordCustomerReqDto) {
    return this.authCustomerService.resetPassword(body);
  }
}

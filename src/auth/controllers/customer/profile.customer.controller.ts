import { Body, Controller, Get, Patch, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthenticateCustomer,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import {
  UpdatePasswordCustomerReqDto,
  UpdateProfileCustomerReqDto,
} from '../../dtos/customer/req/profile.customer.req.dto';
import { User } from '../../entities/user.entity';
import { ProfileCustomerService } from '../../services/customer/profile.customer.service';

@Controller(`${PrefixType.CUSTOMER}/profile`)
@AuthenticateCustomer()
@ApiTags('Profile Customer')
export class ProfileCustomerController {
  constructor(
    private readonly profileCustomerService: ProfileCustomerService,
  ) {}

  @Get()
  getProfile(@CurrentAuthData() user: User) {
    return this.profileCustomerService.getProfile(user);
  }

  @Patch()
  updateProfile(
    @CurrentAuthData() user: User,
    @Body() body: UpdateProfileCustomerReqDto,
  ) {
    return this.profileCustomerService.updateProfile(user, body);
  }

  @Patch('/update-password')
  @UseGuards(ThrottlerGuard)
  updatePassword(
    @CurrentAuthData() user: User,
    @Body() body: UpdatePasswordCustomerReqDto,
  ) {
    return this.profileCustomerService.updatePassword(user, body);
  }
}

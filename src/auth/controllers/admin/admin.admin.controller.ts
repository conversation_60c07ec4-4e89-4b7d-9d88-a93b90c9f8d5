import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from '../../../common/constants/global.constant';
import {
  AuthorizeAdmin,
  CurrentAuthData,
} from '../../../common/decorators/auth.decorator';
import { DeleteMultipleByIdNumberReqDto } from '../../../common/dtos/delete-multiple.dto';
import { Action, Resource } from '../../../common/enums/casl.enum';
import {
  CreateAdminReqDto,
  ListAdminReqDto,
  UpdateAdminReqDto,
} from '../../dtos/admin/req/admin.admin.req.dto';
import { User } from '../../entities/user.entity';
import { AdminAdminService } from '../../services/admin/admin.admin.service';

@Controller(`${PrefixType.ADMIN}/admin`)
@ApiTags('Manage Admin')
@AuthorizeAdmin({ action: Action.MANAGE, resource: Resource.ADMIN })
export class AdminAdminController {
  constructor(private adminAdminService: AdminAdminService) {}

  @Get()
  getListAdmin(@Query() body: ListAdminReqDto, @CurrentAuthData() user: User) {
    return this.adminAdminService.getList(body, user);
  }

  @Get(':id')
  getAdminDetail(@Param('id') id: number, @CurrentAuthData() user: User) {
    return this.adminAdminService.getDetail(id, user);
  }

  @Post()
  create(@Body() body: CreateAdminReqDto, @CurrentAuthData() user: User) {
    return this.adminAdminService.create(body, user);
  }

  @Patch()
  update(@Body() body: UpdateAdminReqDto, @CurrentAuthData() user: User) {
    return this.adminAdminService.update(body, user);
  }

  @Delete()
  deleteListAdmin(
    @Body() body: DeleteMultipleByIdNumberReqDto,
    @CurrentAuthData() user: User,
  ) {
    return this.adminAdminService.deleteList(body, user);
  }

  @Delete(':id')
  deleteAdminById(@Param('id') id: number, @CurrentAuthData() user: User) {
    return this.adminAdminService.deleteSingle(id, user);
  }
}

import { BaseEntity } from 'src/common/entities/base.entity';
import {
  Column,
  Entity,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { GroupPolicy } from '../../casl/entities/group-policy.entity';
import { UserToGroupPolicy } from '../../casl/entities/user-to-group-policy.entity';
import { File } from '../../file/entities/file.entity';
import { UserType } from '../enums/user.enum';
import { Admin } from './admin.entity';
import { Customer } from './customer.entity';

import { UserGroupToUser } from './user-group-to-user.entity';
import { UserGroup } from './user-group.entity';

@Entity({ name: 'user' })
export class User extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: UserType, default: UserType.CUSTOMER })
  type: UserType;

  @OneToOne(() => Admin, (admin) => admin.user)
  admin: Admin;

  @OneToOne(() => Customer, (customer) => customer.user)
  customer: Customer;

  @OneToMany(
    () => UserToGroupPolicy,
    (userToGroupPolicies) => userToGroupPolicies.user,
  )
  userToGroupPolicies: UserToGroupPolicy[];

  @OneToMany(() => File, (file) => file.uploader)
  files: File[];

  @OneToMany(() => GroupPolicy, (groupPolicy) => groupPolicy.owner)
  groupPolicies: GroupPolicy[];

  @OneToMany(() => UserGroupToUser, (userGroupToUse) => userGroupToUse.user)
  userGroupToUsers: UserGroupToUser[];

  @OneToMany(() => UserGroup, (userGroup) => userGroup.owner)
  userGroups: UserGroup[];
}

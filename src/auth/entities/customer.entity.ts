import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { PartialIndexWithSoftDelete } from '../../common/decorators/typeorm.decorator';
import { BaseEntity } from '../../common/entities/base.entity';
import { CustomerGender, CustomerStatus } from '../enums/customer.enum';
import { User } from './user.entity';

@Entity('customer')
@PartialIndexWithSoftDelete(['phoneNumber'], { unique: true })
export class Customer extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'phone_number', length: 50, nullable: true, unique: true })
  phoneNumber: string;

  @Column({ length: 255, nullable: true, unique: true })
  email: string;

  @Column({ length: 255, nullable: true })
  password: string;

  @Column({ name: 'name', length: 50, nullable: true })
  name: string;

  @Column({ name: 'address', length: 255, nullable: true })
  address: string;

  @Column({
    type: 'enum',
    enum: CustomerStatus,
    default: CustomerStatus.ACTIVE,
  })
  status: CustomerStatus;

  @Column({ name: 'birth_date', type: 'timestamptz', nullable: true })
  birthDate: Date;

  @Column({ type: 'enum', enum: CustomerGender, nullable: true })
  gender: CustomerGender;

  @Column({ name: 'last_visit_date', type: 'timestamptz', nullable: true })
  lastVisitDate: Date;

  @Column({ name: 'avatar_url', length: 255, nullable: true })
  avatarUrl: string;

  // Join user
  @Column({ name: 'user_id' })
  userId: number;

  @OneToOne(() => User, (user) => user.customer)
  @JoinColumn({ name: 'user_id' })
  user: User;
  // End join user
}

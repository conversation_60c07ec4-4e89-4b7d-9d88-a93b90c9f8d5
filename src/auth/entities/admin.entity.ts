import { BaseEntity } from 'src/common/entities/base.entity';
import {
  Colum<PERSON>,
  <PERSON><PERSON><PERSON>,
  Join<PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { UniqueWithSoftDelete } from '../../common/decorators/typeorm.decorator';
import { AdminStatus } from '../enums/admin.enum';
import { User } from './user.entity';

@Entity('admin')
export class Admin extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  @UniqueWithSoftDelete()
  email: string;

  @Column({ select: false })
  password: string;

  @Column({ enum: AdminStatus, type: 'enum' })
  status: AdminStatus;

  @Column({ length: 255, nullable: true })
  name: string;

  @Column({ name: 'avatar_url', length: 255, nullable: true })
  avatarUrl: string;

  // Join user
  @Column({ name: 'user_id' })
  userId: number;

  @OneToOne(() => User, (user) => user.admin)
  @JoinColumn({ name: 'user_id' })
  user: User;
  // End join user
}

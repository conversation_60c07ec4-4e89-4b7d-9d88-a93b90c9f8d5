import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityWithoutUpdateAndVersion } from '../../common/entities/base.entity';
import { CustomerTokenType } from '../enums/customer-token.enum';

@Entity()
export class CustomerToken extends BaseEntityWithoutUpdateAndVersion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ enum: CustomerTokenType, type: 'enum' })
  type: CustomerTokenType;

  @Column({ length: 256 })
  token: string;

  @Column({ nullable: true, type: 'timestamptz' })
  expiresAt: Date;

  @Column({ length: 255 })
  phoneNumber: string;
}

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { BaseEntity } from '../../common/entities/base.entity';
import { UserGroup } from './user-group.entity';

@Entity({ name: 'user_group_to_user' })
export class UserGroupToUser extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  // join userGroup
  @Column({ name: 'user_group_id' })
  userGroupId: number;

  @ManyToOne(() => UserGroup, (userGroup) => userGroup.userGroupToUsers)
  @JoinColumn()
  userGroup: UserGroup;
  //end join userGroup

  // join customer user id
  @Column({ name: 'user_id' })
  userId: number;

  @ManyToOne(() => User, (user) => user.userGroupToUsers)
  @JoinColumn()
  user: User;
  //end join customer user id
}

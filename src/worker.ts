import { createBullBoard } from '@bull-board/api';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { FastifyAdapter as BullBoardFastifyAdapter } from '@bull-board/fastify';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import * as Queue from 'bull';
import { bullConfig } from './common/config/bull.config';
import { AppEnvironment } from './common/enums/app.enum';
import { QueueName } from './worker/enums/worker.enum';
import { WorkerModule } from './worker/worker.module';

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(
    WorkerModule,
    new FastifyAdapter(),
  );

  const bullAdminUrl = '/bull';

  const bullBoardAdapter = new BullBoardFastifyAdapter();
  bullBoardAdapter.setBasePath(bullAdminUrl);

  const { createClient } = await bullConfig.useFactory(app.get(ConfigService));

  createBullBoard({
    queues: Object.values(QueueName).map(
      (item) =>
        new BullAdapter(new Queue(item, { createClient }), {
          allowRetries:
            process.env.NODE_ENV === AppEnvironment.PRODUCTION ? false : true,
        }),
    ),
    serverAdapter: bullBoardAdapter,
  });

  // app.register(bullBoardAdapter.registerPlugin(), {
  //   prefix: bullAdminUrl,
  //   basePath: '*',
  // });
  app.register(bullBoardAdapter.registerPlugin(), {
    prefix: bullAdminUrl,
  });
  await app.listen(+process.env.WORKER_PORT || 5001, '0.0.0.0');
  console.log('Worker is running on: ', await app.getUrl());
}
bootstrap();

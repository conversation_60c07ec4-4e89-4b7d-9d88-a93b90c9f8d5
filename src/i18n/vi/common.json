{"word": {"name": "tên", "notFound": "kh<PERSON>ng tìm thấy", "birthDate": "<PERSON><PERSON><PERSON> sinh nh<PERSON>t", "firebaseIdToken": "token firebase", "referralCode": "mã giới thiệu", "invalid": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>p l<PERSON>", "password": "m<PERSON><PERSON>", "addPointCode": "mã tích điểm", "downloadFileHistory": "l<PERSON><PERSON> sử tải tệp", "fileRequest": "y<PERSON><PERSON> c<PERSON>u t<PERSON>p", "fileRequestQrToGift": "y<PERSON><PERSON> cầu tệp qr đổi quà", "fileRequestQrToPoint": "y<PERSON><PERSON> c<PERSON>u tệp qr tích điểm", "qrCode": "mã qr", "fileRequestDownloadHistory": "l<PERSON><PERSON> sử tải tệp", "systemConfigPoint": "cấu hình điểm hệ thống", "admin": "<PERSON><PERSON><PERSON><PERSON> quản trị", "customer": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "userGroup": "nhóm người dùng", "userGroupToUser": "userGroupToUser", "customerToken": "mã x<PERSON>c thực", "user": "ng<PERSON><PERSON><PERSON> dùng", "cartLineItem": "sản phẩm giỏ hàng", "cart": "giỏ hàng", "groupPolicy": "<PERSON><PERSON><PERSON><PERSON> quyền", "groupToPolicy": "groupToPolicy", "policy": "q<PERSON><PERSON><PERSON>", "userToGroupPolicy": "userToGroupPolicy", "categoryDetail": "chi ti<PERSON><PERSON> danh mục", "category": "danh mục", "cronJobExecutionInfo": "thông tin thực hiện cronJob", "cronJob": "cron<PERSON><PERSON>", "eventHistory": "l<PERSON><PERSON> sử trúng gi<PERSON>i sự kiện", "eventReward": "qu<PERSON> sự kiện", "eventSku": "sku sự kiện", "event": "s<PERSON> ki<PERSON>n", "requestExport": "yêu c<PERSON>u xu<PERSON>t file", "expressDeliveryHistory": "l<PERSON>ch sử vận chuyển express", "file": "t<PERSON><PERSON>", "gameCode": "mã trò ch<PERSON>i", "gameGift": "quà trò ch<PERSON>i", "gameUserInfo": "thông tin người dùng chơi trò chơi", "gameWinHistory": "l<PERSON><PERSON> sử trúng gi<PERSON>i chơi trò chơi", "game": "trò ch<PERSON>", "requestImport": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> n<PERSON> tệp", "notiToUser": "notiToUser", "noti": "thông báo", "userNotiSetting": "userNotiSetting", "externalProductUsedInfo": "thông tin sử dụng sản phẩm bên thứ 3", "orderBilling": "thông tin thanh toán", "orderLineItem": "sản ph<PERSON>m thanh toán", "orderShipping": "thông tin giao hàng", "order": "<PERSON><PERSON><PERSON> hàng", "orderEVoucher": "<PERSON><PERSON><PERSON> hàng eVoucher", "blockedHistory": "l<PERSON><PERSON> s<PERSON> k<PERSON>", "scanHistory": "l<PERSON><PERSON> s<PERSON> quét", "userHistoryPoint": "l<PERSON>ch sử tích điểm", "userNumberScan": "s<PERSON> lần quét của người dùng", "userPointMat": "đi<PERSON><PERSON> người dùng MAT", "userPoint": "đi<PERSON>m người dùng", "externalProductProvider": "n<PERSON><PERSON> cung cấp sản ph<PERSON>m bên thứ 3", "externalProduct": "s<PERSON><PERSON> p<PERSON><PERSON><PERSON> bê<PERSON> 3", "productAttributeDetail": "chi ti<PERSON><PERSON> thu<PERSON><PERSON> t<PERSON>h sản phẩm", "productAttributeTermDetail": "chi tiết giá trị thuộc t<PERSON>h sản phẩm", "productAttributeTerm": "gi<PERSON> trị thuộc t<PERSON>h sản phẩm", "productAttribute": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> sản ph<PERSON>m", "productCategory": "danh mục sản phẩm", "productDetail": "chi ti<PERSON><PERSON> sản ph<PERSON>m", "productToAttribute": "productToAttribute", "productToTag": "productToTag", "productToVariant": "productToVariant", "productTransportInfo": "thông tin vận chuyển sản phẩm", "productVariantDetail": "chi tiết biến thể sản phẩm", "productVariantPoint": "đi<PERSON>m biến thể sản phẩm", "productVariantImage": "h<PERSON>nh <PERSON>nh biến thể sản phẩm", "productVariant": "<PERSON><PERSON>n thể sản phẩm", "product": "s<PERSON><PERSON> p<PERSON>m", "province": "tỉnh thành", "externalReferralHistory": "l<PERSON><PERSON> sử giới thi<PERSON>u c<PERSON>a bên thứ 3", "externalReferrer": "thông tin giới thi<PERSON>u bên thứ 3", "referralHistory": "l<PERSON><PERSON> sử giới thi<PERSON>u", "surveyAnswer": "<PERSON><PERSON><PERSON> s<PERSON>t", "surveyQuestion": "câu hỏi kháo sát", "survey": "<PERSON><PERSON><PERSON><PERSON>t", "userSurvetAnswer": "<PERSON><PERSON><PERSON> án kh<PERSON>o sát của người dùng", "userSurveyHistory": "lịch sử tham gia kh<PERSON>o sát của ngư<PERSON>i dùng", "secret": "b<PERSON> m<PERSON>t", "systemConfig": "c<PERSON><PERSON> hình hệ thống", "tag": "thẻ", "termPolicyDetail": "chi ti<PERSON>t ch<PERSON>h sách và điều <PERSON>n", "termsPolicy": "ch<PERSON><PERSON> s<PERSON>ch và đi<PERSON>n", "tierConfig": "c<PERSON><PERSON> hình thứ hạng", "phoneNumber": "s<PERSON> đi<PERSON>n tho<PERSON>i", "deviceToken": "mã thi<PERSON>t bị", "email": "email", "address": "đ<PERSON>a chỉ", "imageId": "id hình <PERSON>nh", "newPassword": "mật kh<PERSON>u mới", "gameType": "lo<PERSON>i trò ch<PERSON>i", "gameGiftConstraint": "rà<PERSON> bu<PERSON>c quà trò chơi", "gameGiftProvinceConstraint": "ràng buộc tỉnh thành quà trò chơi", "gameGiftAllocationConstraint": "ràng buộc chỉ định quà trò chơi", "gamePlayHistory": "l<PERSON><PERSON> s<PERSON> l<PERSON><PERSON><PERSON>", "gamePlayTimeConfig": "c<PERSON>u hình l<PERSON> chơi vòng quay", "gamePlayTime": "<PERSON><PERSON><PERSON><PERSON> chơi vòng quay", "appConfig": "c<PERSON>u hình phiên bản hệ thống", "customerShipping": "đ<PERSON>a chỉ giao hàng", "orderRefund": "<PERSON><PERSON><PERSON> hàng hoàn xu", "store": "c<PERSON><PERSON> h<PERSON>ng", "subject": "<PERSON><PERSON> đề tin tức", "subjectDetail": "<PERSON> tiết chủ đề tin tức", "news": "<PERSON> tức", "newsDetail": "<PERSON> tiết tin tức", "feedback": "Góp ý", "provinceKey": "provinceKey", "newsToSubject": "newsToSubject", "newsToFile": "newsToFile", "gameGiftProvinceQuantity": "Số lượng giải của tỉnh thành quà vòng quay ngẫu nhiên", "externalToken": "<PERSON><PERSON> x<PERSON>c thực bên thứ 3", "outboxMessage": "Outbox Message", "inboxMessage": "Inbox Message", "notiToUserGroup": "<PERSON><PERSON><PERSON><PERSON> báo đến nhóm người dùng", "sfNotiCustomer": "Thông báo khách hàng của sale force", "sfOrderLine": "SF Order Line", "sfTranasction": "SF Transaction", "irisTransactionLog": "Iris Transaction Log"}, "exc": {"badRequest": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy {name}", "internalServerError": "<PERSON><PERSON> thống có lỗi", "forbidden": "Bạn không có quyền để sử dụng dịch vụ này", "unauthorized": "<PERSON>ạn cần đăng nhập để sử dụng dịch vụ này", "conflict": "{name} đ<PERSON> tồn tại", "expectationFailed": "<PERSON><PERSON> lỗi x<PERSON> ra, vui lòng liên hệ hỗ trợ", "serviceUnavailable": "<PERSON><PERSON><PERSON> vụ tạm thời không khả dụng"}, "validationError": {"date": "<PERSON><PERSON><PERSON> h<PERSON> l<PERSON>"}}
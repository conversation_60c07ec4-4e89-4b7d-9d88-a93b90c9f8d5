{"common": {"deleteMultipleError": "Invalid id list", "wrongOldPassword": "Wrong password", "invalidEmail": "Email format is invalid", "invalidUser": "User is invalid", "invalidToken": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập không hợp lệ", "expiredToken": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập đã hết hạn"}, "customer": {"passwordRequired": "<PERSON><PERSON><PERSON> kh<PERSON>u không đư<PERSON><PERSON> để trống", "wrongPhoneNumber": "Invalid phone number", "wrongPassword": "Invalid password", "failPhoneNumber": "Fail PhoneNumber", "failPassword": "Fail Password ", "phoneNumberNotFound": "Phone number not found", "customerNotFound": "User not found", "blockedAccount": " Your account is blocked", "unBlockAccount": "Your account has unBlocked already", "blockedScanQrCode": " Your scan qr code activity is blocked ", "unBlockedScanQrCode": " Your scan qr code activity has unBlocked already", "existed": "User already exists", "zaloLoginFailed": "<PERSON><PERSON> login failed"}, "admin": {"invalidEmail": "<PERSON><PERSON><PERSON> d<PERSON>ng email không hợp lệ", "wrongPassword": "Password format is invalid", "failUserName": "User name not found", "adminNotFound": "User not found"}, "userGroup": {"userGroupNotFound": "UserGroup not found", "deleteMultipleError": "Delete multiple error"}, "agent": {"agentNotFound": "User not found"}, "mustUpdateAllProvinceDistrictWard": "<PERSON><PERSON><PERSON> cập nhật tất cả tỉnh, qu<PERSON><PERSON>, huy<PERSON><PERSON>", "provinceNotFound": "Tỉnh thành/Thành phố không tồn tại", "districtNotFound": "Quận/Huyện không tồn tại", "wardNotFound": "Phường/<PERSON>ã không tồn tại", "districtNotMatchParent": "{district} không thuộc {province}", "wardNotMatchParent": "{ward} kh<PERSON><PERSON> thu<PERSON> {district}", "provinceTypeIsNotProvince": "{province} không phải là tỉnh thành/thành phố", "provinceTypeIsNotDistrict": "{province} không phải là quận/huyện", "provinceTypeIsNotWard": "{province} không phải là tỉnh phường/xã", "customerShippingNotFound": "Địa chỉ giao hàng không tồn tại", "cannotDeleteLastCustomerShipping": "<PERSON><PERSON><PERSON><PERSON> thể xoá địa chỉ giao hàng cuối cùng", "offForgetPassword": "<PERSON><PERSON><PERSON> năng quên mật khẩu đang tạm khoá", "onThresholdSendOtp": "<PERSON><PERSON> lòng chờ đến {time} r<PERSON><PERSON> thử lại", "blockedLogin": "Bạn đã bị khoá đăng nhập do nhập sai mật khẩu quá nhiều lần. <PERSON><PERSON> lòng chờ đến {time}."}
import { ConfigService } from '@nestjs/config';
import {
  DataSource,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  LoadEvent,
} from 'typeorm';
import { GlobalConfig } from '../../common/config/global.config';
import { File } from '../entities/file.entity';

@EventSubscriber()
export class FileSubscriber implements EntitySubscriberInterface<File> {
  constructor(
    dataSource: DataSource,
    private configService: ConfigService<GlobalConfig>,
  ) {
    dataSource.subscribers.push(this);
  }

  listenTo(): string | Function {
    return File;
  }

  afterLoad(entity: File, event?: LoadEvent<File>): void | Promise<any> {
    const file = event.entity;

    if (file.url) return;

    event.entity.url = this.createUrl(entity.key);
  }

  afterInsert(event: InsertEvent<File>): void | Promise<any> {
    const file = event.entity;

    if (file.url) return;

    event.entity.url = this.createUrl(file.key);
  }

  createUrl(key: string): string {
    const domain = this.configService.get('aws.s3.domain', { infer: true });
    return `${domain}/${key}`;
  }
}

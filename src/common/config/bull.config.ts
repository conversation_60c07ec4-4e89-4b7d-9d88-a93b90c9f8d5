import { BullModuleOptions, SharedBullAsyncConfiguration } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';
import Redis, { RedisOptions } from 'ioredis';
import { QueueName } from 'src/worker/enums/worker.enum';
import { AppEnvironment } from '../enums/app.enum';
import { GlobalConfig } from './global.config';

export const bullConfig: SharedBullAsyncConfiguration = {
  inject: [ConfigService],
  useFactory(configService: ConfigService<GlobalConfig>) {
    return {
      createClient(type, redisOpts: RedisOptions) {
        const opts: RedisOptions = {
          ...redisOpts,
          ...(type !== 'client'
            ? { enableReadyCheck: false, maxRetriesPerRequest: null }
            : {}),
        };

        const redisHost = configService.get('redis.standAlone.host');
        const redisPort = configService.get('redis.standAlone.port');
        const password = configService.get('redis.password');

        let redisConfig: RedisOptions;

        switch (configService.get('environment')) {
          case AppEnvironment.LOCAL:
            redisConfig = {
              ...opts,
              host: redisHost,
              port: Number(redisPort),
              password,
            };
            break;
          case AppEnvironment.TEST:
            redisConfig = { ...opts, host: '*********', port: 6379, password };
            break;
          default:
            redisConfig = {
              ...opts,
              host: redisHost,
              port: Number(redisPort),
              password,
            };
            break;
        }

        return new Redis(redisConfig);
      },
      defaultJobOptions: { removeOnComplete: true, removeOnFail: true },
    };
  },
};

export const bullQueues: BullModuleOptions[] = [
  {
    name: QueueName.EXPORT,
    prefix: `{${QueueName.EXPORT}}`,
    defaultJobOptions: {
      attempts: 3,
      backoff: { type: 'exponential', delay: 5000 },
    },
    settings: { stalledInterval: 600000 },
  },
  {
    name: QueueName.IMPORT,
    prefix: `{${QueueName.IMPORT}}`,
    defaultJobOptions: {
      attempts: 3,
      backoff: { type: 'exponential', delay: 5000 },
    },
  },
];

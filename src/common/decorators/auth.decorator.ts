import {
  applyDecorators,
  createParamDecorator,
  ExecutionContext,
  SetMetadata,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiHeader } from '@nestjs/swagger';
import { FastifyRequest } from 'fastify';
import { JwtAuthenAdminGuard } from '../../auth/guards/jwt-authen.admin.guard';
import { JwtAuthenCustomerGuard } from '../../auth/guards/jwt-authen.customer.guard';

import { JwtAuthenUserGuard } from '../../auth/guards/jwt-authen.user.guard';
import { JwtAbilityAdminGuard } from '../../casl/guard/ability.admin.guard';

import { ABILITY_METADATA_KEY } from '../constants/global.constant';
import { BadRequestExc } from '../exceptions/custom.exception';
import { RequiredRule } from '../interfaces/casl.interface';

export const IS_PUBLIC_KEY = Symbol();
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);

export const AuthenticateCustomer = () =>
  applyDecorators(UseGuards(JwtAuthenCustomerGuard), ApiBearerAuth());

export const AuthenticateAdmin = () =>
  applyDecorators(UseGuards(JwtAuthenAdminGuard), ApiBearerAuth());

export const AuthenticateUser = () =>
  applyDecorators(UseGuards(JwtAuthenUserGuard), ApiBearerAuth());

export const AuthorizeAdmin = (...requirements: RequiredRule[]) => {
  return applyDecorators(
    UseGuards(JwtAbilityAdminGuard),
    SetMetadata(ABILITY_METADATA_KEY, requirements),
    ApiBearerAuth(),
  );
};

export const CurrentAuthData = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);

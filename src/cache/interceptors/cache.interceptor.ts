import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { FastifyReply, FastifyRequest } from 'fastify';
import { Pagination } from 'nestjs-typeorm-paginate';
import { defaultIfEmpty, EMPTY, lastValueFrom, Observable } from 'rxjs';
import { CACHE_KEY_METADATA } from '../constants/cache.constant';
import { CacheQuery } from '../dtos/internal/cache.int.dto';
import { CacheKey } from '../enums/cache.enum';
import { CacheResponse } from '../interfaces/cache.interface';
import { CacheService } from '../services/cache.service';

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(
    private cacheService: CacheService,
    private reflector: Reflector,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: <PERSON>Handler<any>,
  ): Promise<Observable<any>> {
    const cacheKey = this.reflector.get<CacheKey>(
      CACHE_KEY_METADATA,
      context.getHandler(),
    );
    if (!cacheKey) return next.handle();

    const request: FastifyRequest = context.switchToHttp().getRequest();
    const response: FastifyReply = context.switchToHttp().getResponse();
    const query = request.query;
    const params = request.params;

    const cacheQueryKeys = Object.keys(new CacheQuery());
    const queryKeys = Object.keys(query);

    for (const index in queryKeys) {
      if (!cacheQueryKeys.includes(queryKeys[index])) return next.handle();
    }

    let cacheData: CacheResponse | null = null;

    try {
      cacheData = await this.cacheService.getData({
        key: cacheKey,
        options: { query, params },
      });
    } catch (err) {
      console.warn('Redis cache GET failed, fallback to handler:', err.message);
    }

    if (!cacheData) {
      const handlerResponse = await lastValueFrom(next.handle());

      if (handlerResponse instanceof Pagination) {
        if (!handlerResponse.items || handlerResponse.items.length === 0)
          return next.handle();
      }

      try {
        const { data, hash } = await this.cacheService.save({
          data: handlerResponse,
          key: cacheKey,
          options: { query, params },
        });
        cacheData = { ...data, hash };
      } catch (err) {
        console.warn(
          'Redis cache SAVE failed, continuing without cache:',
          err.message,
        );
        return next.handle(); // fallback, skip cache store
      }
    }

    response.send(cacheData);
    return EMPTY.pipe(defaultIfEmpty([]));
  }
}

import { Controller, Get, Query, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { GetCacheReqDto } from '../dtos/req/cache.req.dto';
import { CacheService } from '../services/cache.service';

@Controller('cache')
@ApiTags('Cache Controller')
export class CacheController {
  constructor(private cacheService: CacheService) {}

  @Get('hash')
  @UseInterceptors()
  getHash(@Query() query: GetCacheReqDto) {
    return this.cacheService.getHash(query);
  }
}

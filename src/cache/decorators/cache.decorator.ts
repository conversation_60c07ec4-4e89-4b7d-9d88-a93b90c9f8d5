import {
  applyDecorators,
  SetMetadata,
  Type,
  UseInterceptors,
} from '@nestjs/common';
import { ApiOkResponse, getSchemaPath } from '@nestjs/swagger';
import { CACHE_KEY_METADATA } from '../constants/cache.constant';
import { CacheKey } from '../enums/cache.enum';
import { CacheInterceptor } from '../interceptors/cache.interceptor';

export const UseCache = <T extends Type<unknown>>(
  cacheKey: CacheKey,
  resDto: T,
) =>
  applyDecorators(
    UseInterceptors(CacheInterceptor),
    SetMetadata(CACHE_KEY_METADATA, cacheKey),
    ApiOkResponse({
      schema: {
        allOf: [
          { $ref: getSchemaPath(resDto) },
          { properties: { hash: { type: 'string' } } },
        ],
      },
    }),
  );

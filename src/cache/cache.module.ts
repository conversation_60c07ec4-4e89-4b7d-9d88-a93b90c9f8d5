import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TypeOrmCustomModule } from 'utility/dist';

import { CacheController } from './controllers/cache.controller';
import { CacheService } from './services/cache.service';

@Module({
  imports: [HttpModule],
  controllers: [CacheController],
  providers: [CacheService],
  exports: [CacheService],
})
export class CacheModule {}

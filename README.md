# 🛠 Loyalty App

This document outlines key commands, structure, and policies for working on this project.

---

## 📦 Migrations (TypeORM)

1. **Create an empty migration file**

   ```bash
   npm run migration:create --name='your_migration_name'
   ```

2. **Generate a migration based on schema changes**

   ```bash
   npm run migration:generate --name='your_migration_name'
   ```

3. **Run all pending migrations**

   ```bash
   npm run migration:up
   ```

4. **Revert the latest migration**
   ```bash
   npm run migration:down
   ```

> 💡 Always review generated migrations before committing them.

---

## 🧪 Git & Husky

- **Bypass Husky on commit**
  ```bash
  git commit -n -m "your message"
  ```

> ⚠️ Use this only when necessary. <PERSON><PERSON> is used to enforce code quality and consistency.

---

## ⏱ Cron Jobs

- **Sync products from UrBox**
  ```bash
  npm run cron-job sync-product-ur-box
  ```

## 📐 Project Structure

```
src/
├── modules/
│   └── <feature-name>/
│       ├── controllers/
│       ├── services/
│       ├── entities/
│       ├── dtos/
│       ├── constants/
│       └── ...
├── common/
│   ├── filters/
│   ├── interceptors/
│   ├── decorators/
│   └── utils/
├── config/
├── database/
│   ├── migrations/
│   └── seeders/
└── main.ts
```

---

## 📏 Conventions & Policies

### 🧾 Database Table Naming

- Use **snake_case** and **plural** names (must end with **`s`**).

  - ✅ `users`, `products`, `order_items`
  - ❌ `User`, `product`, `OrderItem`

- Junction tables for Many-to-Many relationships should be in alphabetical order:
  - ✅ `product_tags`
  - ✅ `user_roles`

### 📌 Code Naming

- `camelCase` → variables, function names
- `PascalCase` → class names, DTOs
- `UPPER_SNAKE_CASE` → constants

### 📁 Module Design

Each module should include:

- Entity
- DTO
- Service
- Controller
- Constants
- Optional: Guard, Middleware, Validation

---

## 📚 Useful Scripts

- **Start development server**

  ```bash
  npm run start:dev
  ```

- **Format code**

  ```bash
  npm run format
  ```

- **Lint check**

  ```bash
  npm run lint
  ```

- **Run tests**
  ```bash
  npm run test
  ```

---

## ⚙️ Environment Variables

Create `.env` file based on `.env.example` and set the following:

```
DATABASE_URL=
REDIS_URL=
JWT_SECRET=
```

> Keep sensitive keys out of version control.

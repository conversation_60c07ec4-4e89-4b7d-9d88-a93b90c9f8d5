import { MigrationInterface, QueryRunner } from "typeorm";

export class InitMigration1754539561800 implements MigrationInterface {
    name = 'InitMigration1754539561800'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "policy" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "version" integer NOT NULL DEFAULT '1', "id" SERIAL NOT NULL, "action" "public"."policy_action_enum" NOT NULL, "resource" "public"."policy_resource_enum" NOT NULL, "action_ability" "public"."policy_action_ability_enum" NOT NULL, "name" character varying NOT NULL, "type" "public"."policy_type_enum" NOT NULL, CONSTRAINT "PK_9917b0c5e4286703cc656b1d39f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_f91781515b77e75acc8699b340" ON "policy" ("name") WHERE deleted_at is null`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_c01ce7c3e1e597fd586713f73a" ON "policy" ("action", "resource", "action_ability") WHERE deleted_at is null`);
        await queryRunner.query(`CREATE TABLE "group_to_policy" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" SERIAL NOT NULL, "policy_id" integer NOT NULL, "group_policy_id" integer NOT NULL, CONSTRAINT "PK_909a49611af01af60a5014ae6f1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_to_group_policy" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" SERIAL NOT NULL, "user_id" integer NOT NULL, "group_policy_id" integer NOT NULL, CONSTRAINT "PK_3d80a3b81391587a08b71632878" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_541549f7999a4c0f0102971f20" ON "user_to_group_policy" ("user_id", "group_policy_id") WHERE deleted_at is null`);
        await queryRunner.query(`CREATE TABLE "group_policy" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "version" integer NOT NULL DEFAULT '1', "id" SERIAL NOT NULL, "key" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "status" "public"."group_policy_status_enum" NOT NULL, "type" "public"."group_policy_type_enum" NOT NULL, "owner_id" integer NOT NULL, CONSTRAINT "PK_6141276c36bd3276acad7ce4fb0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "admin" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "version" integer NOT NULL DEFAULT '1', "id" SERIAL NOT NULL, "email" character varying NOT NULL, "password" character varying NOT NULL, "status" "public"."admin_status_enum" NOT NULL, "name" character varying(255), "avatar_url" character varying(255), "user_id" integer NOT NULL, CONSTRAINT "REL_a28028ba709cd7e5053a86857b" UNIQUE ("user_id"), CONSTRAINT "PK_e032310bcef831fb83101899b10" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_a493aebc763207e24591d04faf" ON "admin" ("email") WHERE deleted_at is null`);
        await queryRunner.query(`CREATE TABLE "customer" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "version" integer NOT NULL DEFAULT '1', "id" SERIAL NOT NULL, "phone_number" character varying(50), "email" character varying(255), "password" character varying(255), "name" character varying(50), "address" character varying(255), "status" "public"."customer_status_enum" NOT NULL DEFAULT 'ACTIVE', "birth_date" TIMESTAMP WITH TIME ZONE, "gender" "public"."customer_gender_enum", "last_visit_date" TIMESTAMP WITH TIME ZONE, "avatar_url" character varying(255), "user_id" integer NOT NULL, CONSTRAINT "UQ_998bb43a16f512608c017301523" UNIQUE ("phone_number"), CONSTRAINT "UQ_fdb2f3ad8115da4c7718109a6eb" UNIQUE ("email"), CONSTRAINT "REL_5d1f609371a285123294fddcf3" UNIQUE ("user_id"), CONSTRAINT "PK_a7a13f4cacb744524e44dfdad32" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_192fbbbbfc2072ce8366c8b4e4" ON "customer" ("phone_number") WHERE deleted_at is null`);
        await queryRunner.query(`CREATE TABLE "user_group" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "version" integer NOT NULL DEFAULT '1', "id" SERIAL NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "status" "public"."user_group_status_enum" NOT NULL, "owner_id" integer NOT NULL, CONSTRAINT "PK_3c29fba6fe013ec8724378ce7c9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_group_to_user" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "version" integer NOT NULL DEFAULT '1', "id" SERIAL NOT NULL, "user_group_id" integer NOT NULL, "user_id" integer NOT NULL, CONSTRAINT "PK_6bc4df4c713b40ef597a440a998" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "version" integer NOT NULL DEFAULT '1', "id" SERIAL NOT NULL, "type" "public"."user_type_enum" NOT NULL DEFAULT 'CUSTOMER', CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "file" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "version" integer NOT NULL DEFAULT '1', "id" SERIAL NOT NULL, "key" character varying, "url" character varying, "type" character varying NOT NULL, "size" integer NOT NULL DEFAULT '0', "uploader_id" integer NOT NULL, CONSTRAINT "PK_36b46d232307066b3a2c9ea3a1d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_d2df77c0cbda74ee650020d9e1" ON "file" ("key") WHERE deleted_at is null`);
        await queryRunner.query(`CREATE TABLE "customer_token" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" SERIAL NOT NULL, "type" "public"."customer_token_type_enum" NOT NULL, "token" character varying(256) NOT NULL, "expires_at" TIMESTAMP WITH TIME ZONE, "phone_number" character varying(255) NOT NULL, CONSTRAINT "PK_74dbc7458b3b35629e393aa81ee" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "group_to_policy" ADD CONSTRAINT "FK_547333b0c9f647f9595e2d0d908" FOREIGN KEY ("policy_id") REFERENCES "policy"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_to_policy" ADD CONSTRAINT "FK_d26b43fcc8f5bfb1ab73ad2e63d" FOREIGN KEY ("group_policy_id") REFERENCES "group_policy"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_to_group_policy" ADD CONSTRAINT "FK_da28d5ea6b706ac14605106e0d6" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_to_group_policy" ADD CONSTRAINT "FK_8d825595996ba4b366af1d0c0f1" FOREIGN KEY ("group_policy_id") REFERENCES "group_policy"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_policy" ADD CONSTRAINT "FK_aa90a3767e9c5de28c0fa071e29" FOREIGN KEY ("owner_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "admin" ADD CONSTRAINT "FK_a28028ba709cd7e5053a86857b4" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "customer" ADD CONSTRAINT "FK_5d1f609371a285123294fddcf3a" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_group" ADD CONSTRAINT "FK_6bb70cc21e35d14030333bab01d" FOREIGN KEY ("owner_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_group_to_user" ADD CONSTRAINT "FK_69eabfdfcaddeb57e771ea143f0" FOREIGN KEY ("user_group_id") REFERENCES "user_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_group_to_user" ADD CONSTRAINT "FK_70b533d58b6b3e76d2854129c4f" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "file" ADD CONSTRAINT "FK_96519432f789c1624978f27ffca" FOREIGN KEY ("uploader_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "file" DROP CONSTRAINT "FK_96519432f789c1624978f27ffca"`);
        await queryRunner.query(`ALTER TABLE "user_group_to_user" DROP CONSTRAINT "FK_70b533d58b6b3e76d2854129c4f"`);
        await queryRunner.query(`ALTER TABLE "user_group_to_user" DROP CONSTRAINT "FK_69eabfdfcaddeb57e771ea143f0"`);
        await queryRunner.query(`ALTER TABLE "user_group" DROP CONSTRAINT "FK_6bb70cc21e35d14030333bab01d"`);
        await queryRunner.query(`ALTER TABLE "customer" DROP CONSTRAINT "FK_5d1f609371a285123294fddcf3a"`);
        await queryRunner.query(`ALTER TABLE "admin" DROP CONSTRAINT "FK_a28028ba709cd7e5053a86857b4"`);
        await queryRunner.query(`ALTER TABLE "group_policy" DROP CONSTRAINT "FK_aa90a3767e9c5de28c0fa071e29"`);
        await queryRunner.query(`ALTER TABLE "user_to_group_policy" DROP CONSTRAINT "FK_8d825595996ba4b366af1d0c0f1"`);
        await queryRunner.query(`ALTER TABLE "user_to_group_policy" DROP CONSTRAINT "FK_da28d5ea6b706ac14605106e0d6"`);
        await queryRunner.query(`ALTER TABLE "group_to_policy" DROP CONSTRAINT "FK_d26b43fcc8f5bfb1ab73ad2e63d"`);
        await queryRunner.query(`ALTER TABLE "group_to_policy" DROP CONSTRAINT "FK_547333b0c9f647f9595e2d0d908"`);
        await queryRunner.query(`DROP TABLE "customer_token"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d2df77c0cbda74ee650020d9e1"`);
        await queryRunner.query(`DROP TABLE "file"`);
        await queryRunner.query(`DROP TABLE "user"`);
        await queryRunner.query(`DROP TABLE "user_group_to_user"`);
        await queryRunner.query(`DROP TABLE "user_group"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_192fbbbbfc2072ce8366c8b4e4"`);
        await queryRunner.query(`DROP TABLE "customer"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a493aebc763207e24591d04faf"`);
        await queryRunner.query(`DROP TABLE "admin"`);
        await queryRunner.query(`DROP TABLE "group_policy"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_541549f7999a4c0f0102971f20"`);
        await queryRunner.query(`DROP TABLE "user_to_group_policy"`);
        await queryRunner.query(`DROP TABLE "group_to_policy"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c01ce7c3e1e597fd586713f73a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f91781515b77e75acc8699b340"`);
        await queryRunner.query(`DROP TABLE "policy"`);
    }

}

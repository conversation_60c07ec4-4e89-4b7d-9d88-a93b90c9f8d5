import { MigrationInterface, QueryRunner } from "typeorm";
import * as bcrypt from 'bcrypt';

export class SeedDevAdmin1754539611942 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // First create a user for the dev admin
        await queryRunner.query(`INSERT INTO "user" ("type") VALUES ('ADMIN')`);

        // Get the user ID that was just created
        const userResult = await queryRunner.query(`SELECT id FROM "user" WHERE "type" = 'ADMIN' ORDER BY id DESC LIMIT 1`);
        const userId = userResult[0].id;

        // Hash the password using bcrypt with the same settings as the application
        const hashedPassword = bcrypt.hashSync('123456', 12);

        // Create the dev admin with the user_id and hashed password
        await queryRunner.query(`INSERT INTO "admin" ("email", "password", "status", "user_id") VALUES ('<EMAIL>', '${hashedPassword}', 'ACTIVE', ${userId})`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Delete the dev admin and its associated user
        await queryRunner.query(`DELETE FROM "admin" WHERE "email" = '<EMAIL>'`);
        await queryRunner.query(`DELETE FROM "user" WHERE "type" = 'ADMIN' AND id NOT IN (SELECT user_id FROM "admin")`);
    }

}

import { MigrationInterface, QueryRunner } from "typeorm";

export class InitMigration1754538682065 implements MigrationInterface {
    name = 'InitMigration1754538682065'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`INSERT INTO "admin" ("username", "password", "status") VALUES ('root', 'root', 'ACTIVE')`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
    }

}

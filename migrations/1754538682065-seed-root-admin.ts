import { MigrationInterface, QueryRunner } from "typeorm";

export class SeedRootAdmin1754538682065 implements MigrationInterface {
    name = 'SeedRootAdmin1754538682065'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // First create a user for the admin
        await queryRunner.query(`INSERT INTO "user" ("type") VALUES ('ADMIN')`);

        // Get the user ID that was just created
        const userResult = await queryRunner.query(`SELECT id FROM "user" WHERE "type" = 'ADMIN' ORDER BY id DESC LIMIT 1`);
        const userId = userResult[0].id;

        // Create the admin with the user_id
        await queryRunner.query(`INSERT INTO "admin" ("username", "password", "status", "user_id") VALUES ('root', 'root', 'ACTIVE', ${userId})`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Delete the root admin and its associated user
        await queryRunner.query(`DELETE FROM "admin" WHERE "username" = 'root'`);
        await queryRunner.query(`DELETE FROM "user" WHERE "type" = 'ADMIN' AND id NOT IN (SELECT user_id FROM "admin")`);
    }

}

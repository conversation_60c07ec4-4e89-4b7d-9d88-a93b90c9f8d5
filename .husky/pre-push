#!/usr/bin/env sh
# . "$(dirname -- "$0")/_/husky.sh"


# yarn branch-name-lint .branchnamelintrc.json 
#!/bin/sh
echo "🧪 Husky pre-push hook is running"
. "$(dirname "$0")/_/husky.sh"

BRANCH=$(git symbolic-ref --short HEAD)

echo "🔍 Checking branch name: $BRANCH"

yarn branch-name-lint .branchnamelintrc.json "$BRANCH"

if [ $? -ne 0 ]; then
  echo "❌ Push aborted due to invalid branch name."
  exit 1
fi

echo "✅ Branch name is valid. Proceeding to push..."
npm run lint && echo '✅ Passed husky check'

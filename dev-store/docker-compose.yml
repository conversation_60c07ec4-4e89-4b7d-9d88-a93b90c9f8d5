# version: '3.9'

# services:
#   mandala-postgres-dev:
#     image: postgres:17.4
#     container_name: mandala-postgres-dev
#     environment:
#       POSTGRES_USER: postgres
#       POSTGRES_PASSWORD: Eazmn3PPplDKX9K9
#       POSTGRES_DB: mandala
#     volumes:
#       - mandala-postgres-dev-data:/var/lib/postgresql/data
#     ports:
#       - '5432:5432'
#     restart: always
#     networks:
#       - mandala-dev-network
#     deploy:
#       resources:
#         limits:
#           cpus: '0.5'
#           memory: 512M
#         reservations:
#           cpus: '0.25'
#           memory: 256M

#   mandala-redis-dev:
#     image: redis:7.4.3
#     container_name: mandala-redis-dev
#     ports:
#       - '6379:6379'
#     command: ['redis-server', '--bind', '0.0.0.0', '--appendonly', 'yes']
#     volumes:
#       - mandala-redis-dev-data:/data
#     restart: always
#     networks:
#       - mandala-dev-network
#     deploy:
#       resources:
#         limits:
#           cpus: '0.3'
#           memory: 256M
#         reservations:
#           cpus: '0.1'
#           memory: 128M

# volumes:
#   mandala-postgres-dev-data:
#     name: mandala-postgres-dev-data
#   mandala-redis-dev-data:
#     name: mandala-redis-dev-data

# networks:
#   mandala-dev-network:
#     driver: bridge
#     name: mandala-dev-network

version: "3.8"
services:
  clever-tube-backend-dev:
    container_name: clever-tube-backend-dev
    image: clever-tube-backend-dev:latest
    restart: always
    expose:
      - "5000"
    env_file:
      - .env
    environment:
      - VIRTUAL_HOST=api.uat.clevertube.tesoglobal.com
      - VIRTUAL_PATH=/api/
      - LETSENCRYPT_HOST=api.uat.clevertube.tesoglobal.com
      - LETSENCRYPT_EMAIL=<EMAIL>
      - VIRTUAL_PORT=5000
    command: yarn start:prod
    networks:
      - external-network
    deploy:
      resources:
        limits:
          cpus: "0.2"
          memory: "256M"
        reservations:
          cpus: "0.1"
          memory: "128M"
  # clever-tube-backend-app-worker:
  #   container_name: clever-tube-backend-app-worker
  #   image: clever-tube-backend-dev:latest
  #   restart: always
  #   expose:
  #     - "5000"
  #   env_file:
  #     - .worker.env
  #   command: yarn start:worker
  #   networks:
  #     - external-network
  #   deploy:
  #     resources:
  #       limits:
  #         cpus: "0.1"
  #         memory: "128M"
  #       reservations:
  #         cpus: "0.02"
  #         memory: "64M"
networks:
  external-network:
    external: true

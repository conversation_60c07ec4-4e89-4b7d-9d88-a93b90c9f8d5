{"name": "mandala-be", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "prepare": "husky install", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "dev": "nest start --watch", "debug": "nest start --debug --watch", "dev:worker": "nest start --config nest-cli-worker.json --watch", "debug:worker": "nest start --config nest-cli-worker.json  --debug --watch", "start": "nest start", "start:prod": "node dist/src/main", "start:worker": "node dist/src/worker", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "cross-env NODE_ENV=TEST jest --forceExit --runInBand", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "cross-env NODE_ENV=TEST node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --forceExit --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "node --require ts-node/register node_modules/typeorm/cli.js", "migration:generate": "npm run build && cross-var npm run typeorm -- -d ./data-source.ts migration:generate ./migrations/$npm_config_name", "migration:up": "npm run build && npm run typeorm migration:run -- -d ./data-source.ts", "migration:down": "npm run build && npm run typeorm -- -d ./data-source.ts migration:revert", "migration:create": "cross-var npm run typeorm -- migration:create ./migrations/$npm_config_name", "cron-job": "node dist/src/cron-job/execute-command.js", "branch-name-lint": "branch-name-lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.802.0", "@aws-sdk/lib-storage": "^3.802.0", "@aws-sdk/s3-presigned-post": "^3.802.0", "@bull-board/api": "^6.12.0", "@bull-board/fastify": "^6.12.0", "@casl/ability": "^6.7.3", "@faker-js/faker": "^9.7.0", "@jorgebodega/typeorm-factory": "^2.1.0", "@liaoliaots/nestjs-redis": "^10.0.0", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/platform-fastify": "^11.1.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.6", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "awesome-phonenumber": "^7.4.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "boolean": "^3.2.0", "bull": "^4.16.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cross-var": "^1.1.0", "dayjs": "^1.11.13", "dotenv": "^17.2.1", "lint-staged": "^16.1.4", "nestjs-i18n": "^10.5.1", "nestjs-typeorm-paginate": "^4.1.0", "node-forge": "^1.3.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.15.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "slugify": "^1.6.6", "typeorm": "^0.3.22", "typeorm-transactional": "^0.5.0", "utility": "git+https://github.com/doankhietthanh/nestjs-utility.git", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.0", "@types/bcrypt": "^5.0.2", "@types/bull": "^4.10.4", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/node": "^24.2.0", "@types/node-forge": "^1.3.11", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "branch-name-lint": "^3.0.1", "eslint": "8", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.3.1", "husky": "8", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"*.{ts,js}": "eslint --fix"}}
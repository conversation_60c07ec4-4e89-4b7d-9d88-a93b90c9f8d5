name: Deploy BE to Server via Docker

on:
  push:
    branches:
      - staging

jobs:
  build:
    name: Build & Deploy BE Clever Tube
    runs-on: ubuntu-latest

    steps:
      # Checkout the code
      - name: Checkout code
        uses: actions/checkout@v3

      # Set up Docker
      - name: Set up Docker
        uses: docker/setup-buildx-action@v2

      # Build Docker image
      - name: Build Docker Image
        run: |
          docker build -t clever-tube-backend-uat .

      # Save Docker image as a tar file
      - name: Save Docker Image
        run: |
          docker save clever-tube-backend-uat > clever-tube-backend-uat.tar

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: clever-tube-artifacts
          path: |
            clever-tube-backend-uat.tar
            docker-compose.yml

  transfer:
    name: 🚚 Transfer Image + Compose to Server
    runs-on: ubuntu-latest
    needs: build

    steps:
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          name: clever-tube-artifacts

      - name: Transfer to VPS
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_IP_UAT }}
          username: ${{ secrets.SERVER_USER_UAT }}
          key: ${{ secrets.SERVER_SSH_KEY_UAT }}
          source: 'clever-tube-backend-uat.tar'
          target: /home/<USER>/sources/uat

  deploy:
    runs-on: ubuntu-latest
    needs: transfer

    steps:
      - name: SSH into VPS and Deploy using Docker Compose
        uses: appleboy/ssh-action@v1.2.0
        with:
          host: ${{ secrets.SERVER_IP_UAT }}
          username: ${{ secrets.SERVER_USER_UAT }}
          key: ${{ secrets.SERVER_SSH_KEY_UAT }}
          script: |
            cd /home/<USER>/sources/uat

            # Load the image
            docker load < clever-tube-backend-uat.tar

            # Create .env from secret
            echo "${{ secrets.ENV_FILE_UAT }}" > .env

            # Stop old containers if any
            docker stop clever-tube-backend-uat || true

            docker rm clever-tube-backend-uat || true

            echo "🧽 Cleaning up old images (dangling & non-latest)..."
            # Xóa các image có <none> tag (dangling)
            docker images | grep '<none>' | awk '{print $3}' | xargs -r docker rmi -f
            docker images | grep 'clever-tube-backend-uat' | grep -v 'latest' | awk '{print $3}' | xargs -r docker rmi -f


            # Start up with latest image
            docker compose up -d --build

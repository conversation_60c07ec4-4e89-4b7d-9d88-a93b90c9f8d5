# FROM node:22-alpine AS builder

# ARG SSH_PRIVATE_KEY
# RUN mkdir /root/.ssh/
# RUN apk update && apk add git openssh
# RUN echo "${SSH_PRIVATE_KEY}" > /root/.ssh/id_rsa
# RUN chmod 600 /root/.ssh/id_rsa
# RUN ssh-keyscan github.com > /root/.ssh/known_hosts

# WORKDIR /usr/src/app
# COPY package.json ./
# RUN yarn install --network-concurrency 1
# COPY . ./
# RUN yarn build 

# EXPOSE 5000
# CMD ["yarn", "start:prod"]
FROM node:20-alpine AS development

# Cài git cho husky & repo git dependencies
RUN apk add --no-cache git

WORKDIR /usr/src/app

COPY package.json yarn.lock ./

# ✨ Dùng thư mục cache riêng hoàn toàn để tránh lỗi `/usr/local/share/.cache/yarn`
ENV YARN_CACHE_FOLDER=/usr/src/app/.yarn-cache

# ✅ Cài đặt với yarn cache sạch
RUN mkdir -p $YARN_CACHE_FOLDER && \
    yarn cache clean && \
    HUSKY=0 yarn install --network-concurrency 1 --frozen-lockfile --non-interactive

COPY . .

RUN yarn build

EXPOSE 5000

CMD ["yarn", "start:prod"]
